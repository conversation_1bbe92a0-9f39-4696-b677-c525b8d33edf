#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检测脚本 - 不使用GUI，直接检测DaYuanTuZ_0.png
"""

import os
import torch
import cv2
import time
from PIL import Image
import json

def check_gpu_memory():
    """检查GPU内存"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        print(f"🖥️  GPU总内存: {gpu_memory:.1f} GB")
        free_memory = torch.cuda.memory_reserved(0) / 1024**3
        print(f"💾 GPU可用内存: {free_memory:.1f} GB")
        return gpu_memory
    else:
        print("❌ 未检测到CUDA GPU")
        return 0

def preprocess_image(image_path, max_size=2560):
    """预处理大图像"""
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            max_dim = max(width, height)
            
            print(f"📐 原始图像尺寸: {width} x {height}")
            
            if max_dim > max_size:
                scale = max_size / max_dim
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                print(f"🔄 缩放图像到: {new_width} x {new_height}")
                
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                temp_path = "temp_resized_detection.jpg"
                resized_img.save(temp_path, quality=95)
                
                return temp_path, scale
            else:
                return image_path, 1.0
                
    except Exception as e:
        print(f"❌ 图像预处理错误: {e}")
        return image_path, 1.0

def detect_with_yolo(image_path, model_path, conf_threshold=0.25, imgsz=2560):
    """使用YOLO进行目标检测"""
    try:
        from ultralytics import YOLO
        
        print(f"🔧 加载模型: {model_path}")
        model = YOLO(model_path)
        
        # 检查GPU内存
        gpu_memory = check_gpu_memory()
        
        # 根据GPU内存选择设备和调整参数
        if gpu_memory < 8:
            device = 'cpu'
            imgsz = min(imgsz, 1280)
            print(f"⚠️  GPU内存不足，使用CPU，图像尺寸调整为: {imgsz}")
        elif gpu_memory < 12:
            device = '0'
            imgsz = min(imgsz, 2560)
            print(f"🔧 使用GPU，图像尺寸调整为: {imgsz}")
        else:
            device = '0'
            print(f"✅ 使用GPU，图像尺寸: {imgsz}")
        
        # 确保尺寸是32的倍数
        if imgsz % 32 != 0:
            imgsz = ((imgsz // 32) + 1) * 32
            print(f"📏 调整为32的倍数: {imgsz}")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 预处理图像
        processed_path, scale_factor = preprocess_image(image_path, max_size=3000)
        
        print(f"🚀 开始检测...")
        print(f"   - 图像: {processed_path}")
        print(f"   - 推理尺寸: {imgsz}")
        print(f"   - 设备: {device}")
        print(f"   - 置信度: {conf_threshold}")
        
        # 进行检测
        results = model.predict(
            processed_path,
            conf=conf_threshold,
            imgsz=imgsz,
            device=device,
            verbose=False,
            save=False,
            show=False,
            half=False,
            max_det=1000
        )
        
        print(f"✅ 检测完成!")
        
        # 处理结果
        detections = []
        if len(results) > 0 and len(results[0].boxes) > 0:
            boxes = results[0].boxes
            print(f"📦 检测到 {len(boxes)} 个目标:")
            
            for i, box in enumerate(boxes):
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = float(box.conf[0])
                class_id = int(box.cls[0])
                
                # 如果图像被缩放，需要还原坐标
                if scale_factor != 1.0:
                    x1, y1, x2, y2 = x1/scale_factor, y1/scale_factor, x2/scale_factor, y2/scale_factor
                
                detection = {
                    'class_id': class_id,
                    'confidence': confidence,
                    'bbox': [float(x1), float(y1), float(x2), float(y2)]
                }
                detections.append(detection)
                
                print(f"  {i+1}. 类别ID: {class_id}, 置信度: {confidence:.3f}, "
                      f"位置: ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
        else:
            print("❌ 未检测到任何目标")
        
        # 清理临时文件
        if processed_path != image_path and os.path.exists(processed_path):
            os.remove(processed_path)
        
        return detections, results
        
    except Exception as e:
        print(f"❌ 检测错误: {e}")
        import traceback
        traceback.print_exc()
        return [], None

def save_results(detections, image_path, output_path="detection_results.json"):
    """保存检测结果"""
    result = {
        'image_path': image_path,
        'timestamp': time.time(),
        'detections': detections,
        'total_detections': len(detections)
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"💾 结果已保存到: {output_path}")

def visualize_results(image_path, detections, output_path="detection_result.jpg"):
    """可视化检测结果"""
    try:
        import cv2
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return
        
        # 绘制检测框
        for detection in detections:
            bbox = detection['bbox']
            class_id = detection['class_id']
            confidence = detection['confidence']
            
            x1, y1, x2, y2 = map(int, bbox)
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"ID:{class_id} {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 保存结果图像
        cv2.imwrite(output_path, image)
        print(f"🖼️  可视化结果已保存到: {output_path}")
        
    except Exception as e:
        print(f"❌ 可视化错误: {e}")

def main():
    """主函数"""
    print("🎯 开始直接检测...")
    
    # 配置参数
    image_path = "DaYuanTuZ_0.png"
    model_path = "animal_detection1/yolo11s_exp1/weights/best.pt"
    conf_threshold = 0.25
    imgsz = 2560  # 初始推理尺寸
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"📁 图像文件: {image_path}")
    print(f"🤖 模型文件: {model_path}")
    
    # 进行检测
    detections, results = detect_with_yolo(
        image_path, model_path, conf_threshold, imgsz
    )
    
    # 保存结果
    if detections:
        save_results(detections, image_path)
        visualize_results(image_path, detections)
        print(f"\n🎉 检测完成! 共检测到 {len(detections)} 个目标")
    else:
        print("\n❌ 未检测到任何目标")

if __name__ == "__main__":
    main()
